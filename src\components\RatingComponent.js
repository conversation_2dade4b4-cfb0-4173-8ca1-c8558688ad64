import React, { useState, useEffect, useCallback } from "react";
import { ratingAPI } from "../services/api";

const RatingComponent = ({ novelId, authorId, currentUserId }) => {
  const [ratings, setRatings] = useState({
    like: 0,
    dislike: 0,
    hate: 0,
    super_like: 0,
  });
  const [userRating, setUserRating] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // 评价类型配置
  const ratingTypes = [
    {
      type: "like",
      icon: "👍",
      label: "点赞",
      color: "text-success",
    },
    {
      type: "super_like",
      icon: "❤️",
      label: "超级喜欢",
      color: "text-danger",
    },
    {
      type: "dislike",
      icon: "👎",
      label: "不喜欢",
      color: "text-warning",
    },
    {
      type: "hate",
      icon: "🤮",
      label: "恶心",
      color: "text-dark",
    },
  ];

  useEffect(() => {
    fetchRatingStats();
    if (currentUserId) {
      fetchUserRating();
    }
  }, [novelId, currentUserId, fetchRatingStats, fetchUserRating]);

  const fetchRatingStats = useCallback(async () => {
    try {
      const response = await ratingAPI.getNovelRatingStats(novelId);
      setRatings(response.data);
    } catch (err) {
      console.error("获取评价统计失败", err);
    }
  }, [novelId]);

  const fetchUserRating = useCallback(async () => {
    try {
      const response = await ratingAPI.getUserRating(novelId);
      setUserRating(response.data.rating);
    } catch (err) {
      console.error("获取用户评价失败", err);
    }
  }, [novelId]);

  const handleRating = async (ratingType) => {
    if (!currentUserId) {
      setError("请先登录");
      return;
    }

    if (currentUserId === authorId) {
      setError("不能给自己的作品评价");
      return;
    }

    setLoading(true);
    setError("");

    try {
      if (userRating === ratingType) {
        // 如果点击的是当前评价，则删除评价
        const response = await ratingAPI.deleteRating(novelId);
        setRatings(response.data.stats);
        setUserRating(null);
      } else {
        // 否则创建或更新评价
        const response = await ratingAPI.upsertRating(novelId, { ratingType });
        setRatings(response.data.stats);
        setUserRating(response.data.userRating);
      }
    } catch (err) {
      setError(err.response?.data?.msg || "操作失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="rating-component">
      {error && <div className="alert alert-danger alert-sm mb-2">{error}</div>}

      <div className="d-flex flex-wrap gap-2">
        {ratingTypes.map((ratingType) => (
          <button
            key={ratingType.type}
            className={`btn btn-sm ${
              userRating === ratingType.type
                ? "btn-primary"
                : "btn-outline-secondary"
            } d-flex align-items-center`}
            onClick={() => handleRating(ratingType.type)}
            disabled={loading || currentUserId === authorId}
            title={
              currentUserId === authorId
                ? "不能给自己的作品评价"
                : ratingType.label
            }
          >
            <span className="me-1" style={{ fontSize: "16px" }}>
              {ratingType.icon}
            </span>
            <span
              className={`badge ${
                userRating === ratingType.type
                  ? "bg-light text-dark"
                  : "bg-secondary"
              }`}
            >
              {ratings[ratingType.type]}
            </span>
          </button>
        ))}
      </div>

      {loading && (
        <div className="text-center mt-2">
          <div className="spinner-border spinner-border-sm" role="status">
            <span className="visually-hidden">处理中...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RatingComponent;
